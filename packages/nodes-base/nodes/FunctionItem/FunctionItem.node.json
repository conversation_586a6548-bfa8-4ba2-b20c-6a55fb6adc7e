{"node": "n8n-nodes-base.functionItem", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Development", "Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/"}], "generic": [{"label": "How uProc scraped a multi-page website with a low-code workflow", "icon": " 🕸️", "url": "https://n8n.io/blog/how-uproc-scraped-a-multi-page-website-with-a-low-code-workflow/"}, {"label": "7 no-code workflow automations for Amazon Web Services", "url": "https://n8n.io/blog/aws-workflow-automation/"}]}, "alias": ["Javascript", "Code", "Custom Code", "JS"], "subcategories": {"Core Nodes": ["Data Transformation"]}}