{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-300, 260], "id": "b14bf20f-78b0-490a-bbc6-d02b1af4c03c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "workflow", "workflowId": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "CI"}, "owner": {"__rl": true, "value": "testOwner", "mode": "name"}, "repository": {"__rl": true, "value": "testRepository", "mode": "name"}}, "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [-80, 260], "id": "061752c9-507c-4b27-ba18-47b21d487aed", "name": "GitHub", "credentials": {"githubApi": {"id": "1", "name": "GitHub account"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [120, 260], "id": "3bc54e8f-eeba-496d-a95f-bb8927eff671", "name": "No Operation, do nothing"}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "GitHub", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}}, "pinData": {"No Operation, do nothing": [{"json": {}}]}}