{"node": "n8n-nodes-base.githubTrigger", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Development"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/github/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.githubtrigger/"}], "generic": [{"label": "How to automatically manage contributions to open-source projects", "icon": "🏷️", "url": "https://n8n.io/blog/automation-for-maintainers-of-open-source-projects/"}, {"label": "5 workflow automations for Matter<PERSON> that we love at n8n", "icon": "🤖", "url": "https://n8n.io/blog/5-workflow-automations-for-mattermost-that-we-love-at-n8n/"}, {"label": "How to set up a no-code CI/CD pipeline with GitHub and TravisCI", "icon": "🎡", "url": "https://n8n.io/blog/how-to-set-up-a-ci-cd-pipeline-with-no-code/"}]}}