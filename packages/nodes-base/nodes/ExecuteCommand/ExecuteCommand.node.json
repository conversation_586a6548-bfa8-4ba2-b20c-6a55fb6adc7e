{"node": "n8n-nodes-base.executeCommand", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "Execute command allows you to run terminal commands on the computer/server hosting your n8n instance. Useful for executing a shell script or interacting with your n8n instance programmatically via the CLI.", "categories": ["Development", "Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/"}], "generic": [{"label": "How uProc scraped a multi-page website with a low-code workflow", "icon": " 🕸️", "url": "https://n8n.io/blog/how-uproc-scraped-a-multi-page-website-with-a-low-code-workflow/"}, {"label": "Why this Product Manager loves workflow automation with n8n", "icon": "🧠", "url": "https://n8n.io/blog/why-this-product-manager-loves-workflow-automation-with-n8n/"}]}, "alias": ["Shell", "Command", "OS", "<PERSON><PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}}