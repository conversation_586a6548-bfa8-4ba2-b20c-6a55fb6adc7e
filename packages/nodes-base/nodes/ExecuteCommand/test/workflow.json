{"name": "Execute Command Test", "nodes": [{"parameters": {}, "id": "9f813425-3fe0-49b6-9fc6-a1779bdb71e3", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 420]}, {"parameters": {"values": {"string": [{"name": "data[0]", "value": "n8n"}, {"name": "data[1]", "value": "test"}]}, "options": {}}, "id": "cf02f88c-e64d-4831-9d5b-954ad21ca3ae", "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [360, 420]}, {"parameters": {"fieldToSplitOut": "data", "options": {}}, "id": "364b8576-cb5d-4ca5-b497-e3437b45831a", "name": "Item Lists", "type": "n8n-nodes-base.itemLists", "typeVersion": 1, "position": [520, 420]}, {"parameters": {"command": "=echo {{ $json.data }}"}, "id": "73854e53-324d-46f8-a222-9a9897807307", "name": "EC Once", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"executeOnce": false, "command": "=echo {{ $json.data }}"}, "id": "b47ec541-6e85-489f-b925-2f9a77681f18", "name": "EC All", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [700, 540]}], "pinData": {"EC Once": [{"json": {"exitCode": 0, "stderr": "", "stdout": "n8n"}}], "EC All": [{"json": {"exitCode": 0, "stderr": "", "stdout": "n8n"}}, {"json": {"exitCode": 0, "stderr": "", "stdout": "test"}}]}, "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Item Lists", "type": "main", "index": 0}]]}, "Item Lists": {"main": [[{"node": "EC Once", "type": "main", "index": 0}, {"node": "EC All", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "de1a454e-43e9-4c2d-b786-18da5d97940f", "id": "389", "meta": {"instanceId": "REMOVED"}, "tags": []}