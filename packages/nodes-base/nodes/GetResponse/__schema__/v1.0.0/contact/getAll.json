{"type": "object", "properties": {"activities": {"type": "string"}, "campaign": {"type": "object", "properties": {"campaignId": {"type": "string"}, "href": {"type": "string"}, "name": {"type": "string"}}}, "changedOn": {"type": "string"}, "contactId": {"type": "string"}, "createdOn": {"type": "string"}, "email": {"type": "string"}, "href": {"type": "string"}, "ipAddress": {"type": "string"}, "note": {"type": "null"}, "origin": {"type": "string"}, "timeZone": {"type": "string"}}, "version": 1}