{"name": "Test ConvertToFile", "nodes": [{"parameters": {}, "id": "f2557b99-e65c-4136-9bc5-7cb328a62d30", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [300, 1040]}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {}}, "id": "71711ee7-9df5-456a-b1d0-def85b8d6669", "name": "Convert to File2", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [880, 540]}, {"parameters": {"jsCode": "return   {\n    \"id\": \"23423532\",\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"notes\": \"Keeps asking about a green light??\",\n    \"country\": \"US\",\n    \"created\": \"1925-04-10\",\n  \"base64\": \"VGhpcyBpcyBzb21lIHRleHQgZW5jb2RlZCBhcyBiYXNlNjQ=\"\n  }"}, "id": "ff53ab4c-43dd-4c35-b066-59d59e4a8209", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [520, 1040]}, {"parameters": {"operation": "text", "options": {}}, "id": "6322369c-fef5-4172-bbd3-8738cbb67e05", "name": "Extract From File", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1100, 540]}, {"parameters": {}, "id": "b5aed50b-65d3-4356-b02d-cbeab7fb3d6e", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1320, 540]}, {"parameters": {"operation": "toText", "sourceProperty": "notes", "options": {}}, "id": "65c4c4ac-da33-4ba1-b337-51ee319a8652", "name": "Convert to Text File", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [880, 740]}, {"parameters": {"operation": "text", "options": {}}, "id": "b2f49de3-5d0d-4eff-8156-89e5a2a0edae", "name": "Extract From Text File", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1100, 740]}, {"parameters": {}, "id": "86d5e979-5ff0-4312-8b4f-7ff83f1eebd6", "name": "Text File Result", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1320, 740]}, {"parameters": {"operation": "to<PERSON><PERSON>", "options": {"format": true}}, "id": "a938ea30-3acb-4618-a80a-6e759ba7d8db", "name": "Convert to JSON (with Formatting)", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [880, 920]}, {"parameters": {"operation": "text", "options": {}}, "id": "fa35a5ed-4746-48c5-b260-314c517cdd45", "name": "Extract From JSON (1)", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1100, 920]}, {"parameters": {"operation": "text", "options": {}}, "id": "4a3c5c68-6621-4463-8623-83a6572ae760", "name": "Extract From JSON (2)", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1100, 1120]}, {"parameters": {}, "id": "dbd03dcb-435c-4af7-ada8-2492c69f1cd6", "name": "JSON Result (with Formatting)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1320, 920]}, {"parameters": {"operation": "to<PERSON><PERSON>", "options": {}}, "id": "c461944d-3141-4a1d-abe1-a74e1f71615f", "name": "Convert to JSON", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [880, 1120]}, {"parameters": {"options": {}}, "id": "c9ca03b3-0f01-49f5-ab9d-ed4497829a09", "name": "Convert to CSV", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [880, 1320]}, {"parameters": {"options": {}}, "id": "1ad12799-7916-4780-aca9-9a966f9e5820", "name": "Extract From CSV", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1100, 1320]}, {"parameters": {"options": {"delimiter": "|"}}, "id": "58caeb17-7434-4808-b8c4-4ca443<PERSON><PERSON>ff", "name": "Convert to CSV (custom delimiter)", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [880, 1520]}, {"parameters": {}, "id": "b113b6d9-7928-4de1-bf7f-e2984d124edf", "name": "CSV Result", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1320, 1320]}, {"parameters": {}, "id": "12554f8d-592a-4500-80f7-42518840f718", "name": "JSON Result", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1320, 1120]}, {"parameters": {}, "id": "8a6ab018-9f38-4a5b-9483-769bf38f0b7c", "name": "CSV Result (custom delimiter)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1320, 1520]}, {"parameters": {"options": {"delimiter": "|"}}, "id": "e8a4114f-5264-4255-982e-4690c950fb86", "name": "Extract From Custom Delimiter", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1100, 1520]}], "pinData": {"No Operation, do nothing": [{"json": {"data": "This is some text encoded as base64"}}], "Text File Result": [{"json": {"data": "Keeps asking about a green light??"}}], "JSON Result (with Formatting)": [{"json": {"data": "[\n  {\n    \"id\": \"23423532\",\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"notes\": \"Keeps asking about a green light??\",\n    \"country\": \"US\",\n    \"created\": \"1925-04-10\",\n    \"base64\": \"VGhpcyBpcyBzb21lIHRleHQgZW5jb2RlZCBhcyBiYXNlNjQ=\"\n  }\n]"}}], "CSV Result": [{"json": {"﻿id": "23423532", "name": "<PERSON>", "email": "<EMAIL>", "notes": "Keeps asking about a green light??", "country": "US", "created": "1925-04-10", "base64": "VGhpcyBpcyBzb21lIHRleHQgZW5jb2RlZCBhcyBiYXNlNjQ="}}], "JSON Result": [{"json": {"data": "[{\"id\":\"23423532\",\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"notes\":\"Keeps asking about a green light??\",\"country\":\"US\",\"created\":\"1925-04-10\",\"base64\":\"VGhpcyBpcyBzb21lIHRleHQgZW5jb2RlZCBhcyBiYXNlNjQ=\"}]"}}], "CSV Result (custom delimiter)": [{"json": {"﻿id": "23423532", "name": "<PERSON>", "email": "<EMAIL>", "notes": "Keeps asking about a green light??", "country": "US", "created": "1925-04-10", "base64": "VGhpcyBpcyBzb21lIHRleHQgZW5jb2RlZCBhcyBiYXNlNjQ="}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Convert to File2", "type": "main", "index": 0}, {"node": "Convert to Text File", "type": "main", "index": 0}, {"node": "Convert to JSON (with Formatting)", "type": "main", "index": 0}, {"node": "Convert to JSON", "type": "main", "index": 0}, {"node": "Convert to CSV", "type": "main", "index": 0}, {"node": "Convert to CSV (custom delimiter)", "type": "main", "index": 0}]]}, "Convert to File2": {"main": [[{"node": "Extract From File", "type": "main", "index": 0}]]}, "Extract From File": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Convert to Text File": {"main": [[{"node": "Extract From Text File", "type": "main", "index": 0}]]}, "Extract From Text File": {"main": [[{"node": "Text File Result", "type": "main", "index": 0}]]}, "Convert to JSON (with Formatting)": {"main": [[{"node": "Extract From JSON (1)", "type": "main", "index": 0}]]}, "Extract From JSON (1)": {"main": [[{"node": "JSON Result (with Formatting)", "type": "main", "index": 0}]]}, "Extract From JSON (2)": {"main": [[{"node": "JSON Result", "type": "main", "index": 0}]]}, "Convert to JSON": {"main": [[{"node": "Extract From JSON (2)", "type": "main", "index": 0}]]}, "Convert to CSV": {"main": [[{"node": "Extract From CSV", "type": "main", "index": 0}]]}, "Extract From CSV": {"main": [[{"node": "CSV Result", "type": "main", "index": 0}]]}, "Convert to CSV (custom delimiter)": {"main": [[{"node": "Extract From Custom Delimiter", "type": "main", "index": 0}]]}, "Extract From Custom Delimiter": {"main": [[{"node": "CSV Result (custom delimiter)", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6555e00a-2c20-46f2-9a2c-fb368d557035", "meta": {"templateCredsSetupCompleted": true, "instanceId": "be251a83c052a9862eeac953816fbb1464f89dfbf79d7ac490a8e336a8cc8bfd"}, "id": "RSOCg1c3be66ZqVH", "tags": []}