{"name": "convert to tests", "nodes": [{"parameters": {}, "id": "35cce987-aa4f-4738-bfcd-b85098948341", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [680, 1100]}, {"parameters": {"fields": {"values": [{"name": "row_number", "type": "numberValue", "numberValue": "2"}, {"name": "country", "stringValue": "uk"}, {"name": "browser", "stringValue": "firefox"}, {"name": "session_duration", "type": "numberValue", "numberValue": "1"}, {"name": "visits", "type": "numberValue", "numberValue": "1"}]}, "include": "none", "options": {}}, "id": "13305747-c966-4f46-90b3-ffff6835b714", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [980, 880]}, {"parameters": {"options": {}}, "id": "b08b269d-0735-4dc4-b5a7-7870f5e115f9", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 420]}, {"parameters": {"operation": "html", "options": {}}, "id": "c68c209f-771f-4d25-b832-3d55ee97e6ff", "name": "Convert to File1", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 600]}, {"parameters": {"operation": "to<PERSON><PERSON>", "options": {}}, "id": "2b6f27ed-a4dc-4a29-905f-f0a921ea6ee7", "name": "Convert to File2", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 780]}, {"parameters": {"operation": "to<PERSON><PERSON>", "mode": "each", "options": {}}, "id": "cd482d12-7547-4eb6-880b-bd2c31ef06d5", "name": "Convert to File3", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 960]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "1fd693e3-4286-49f4-ba45-70584c1d67f7", "name": "Convert to File5", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 1140]}, {"parameters": {"options": {}}, "id": "636866e9-ca0d-44a3-b27a-90cf33e26485", "name": "Extract From File", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 420]}, {"parameters": {"operation": "html", "options": {}}, "id": "5c66b2ea-94b2-4a1b-a34d-acb07fe33e13", "name": "Extract From File1", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 600]}, {"parameters": {"operation": "fromJson", "options": {}}, "id": "a03752f0-e3bb-4dd3-9aae-dc4d1471c281", "name": "Extract From File2", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 780]}, {"parameters": {"operation": "fromJson", "options": {}}, "id": "eb10c006-60d7-4842-b9e5-a364f42dd1ab", "name": "Extract From File3", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 960]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "64c98172-2a77-4e83-b19b-232899df8113", "name": "Extract From File4", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 1140]}, {"parameters": {"operation": "xls", "options": {}}, "id": "edbfd57e-d36b-470d-9e8d-9c7f67c131b4", "name": "Convert to File6", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 1320]}, {"parameters": {"operation": "xls", "options": {}}, "id": "86713806-8dc6-45ec-a710-e80b839ec193", "name": "Extract From File5", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 1320]}, {"parameters": {"fields": {"values": [{"name": "base64", "stringValue": "VGhpcyBpcyB0ZXh0IGNvbnZlcnRlZCB0byBiYXNlIDY0"}]}, "include": "none", "options": {}}, "id": "c205d380-2459-4d16-bb56-f862c53c25de", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [980, 1060]}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {}}, "id": "3f5fe04c-63cf-47d0-a7ca-d427b95a0c52", "name": "Convert to File7", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 1880]}, {"parameters": {"operation": "text", "options": {}}, "id": "dbb4eae0-f0ee-4453-aed7-7d8322974c94", "name": "Extract From File6", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 1880]}, {"parameters": {"operation": "ods", "options": {}}, "id": "e3bf810d-7795-4663-82d1-768f76adc0d9", "name": "Convert to File8", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 1520]}, {"parameters": {"operation": "ods", "options": {}}, "id": "7713d273-2d51-4e3e-8ac9-9a4e6013c690", "name": "Extract From File7", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 1520]}, {"parameters": {"operation": "rtf", "options": {}}, "id": "631b29cb-dcde-42cc-a514-45622923dab6", "name": "Convert to File9", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 1700]}, {"parameters": {"operation": "rtf", "options": {}}, "id": "168b6586-c89d-4b0e-880e-4ddb8ea7cb2f", "name": "Extract From File8", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 1700]}, {"parameters": {"operation": "iCal", "start": "2024-01-03T00:00:00", "end": "2024-01-04T00:00:00", "allDay": true, "additionalFields": {"description": "event description"}}, "id": "2ba4acd9-2677-4b25-9379-48433ac5e9cc", "name": "Convert to File10", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [1380, 2100]}, {"parameters": {"operation": "fromIcs", "options": {}}, "id": "c0179d40-7de0-4e42-a6bf-97c5bb764665", "name": "Extract From File9", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1600, 2100]}, {"parameters": {"fields": {"values": [{"name": "description", "stringValue": "={{ $json.data.events[0].description }}"}]}, "include": "none", "options": {}}, "id": "8ee98090-f3b0-41d5-8122-d27e5559738f", "name": "Edit Fields2", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1820, 2100]}], "pinData": {"Extract From File": [{"json": {"﻿row_number": "2", "country": "uk", "browser": "firefox", "session_duration": "1", "visits": "1"}}], "Extract From File1": [{"json": {"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}}], "Extract From File2": [{"json": {"data": [{"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}]}}], "Extract From File3": [{"json": {"data": {"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}}}], "Extract From File4": [{"json": {"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}}], "Extract From File5": [{"json": {"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}}], "Extract From File7": [{"json": {"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}}], "Extract From File6": [{"json": {"data": "This is text converted to base 64"}}], "Extract From File8": [{"json": {"row_number": 2, "country": "uk", "browser": "firefox", "session_duration": 1, "visits": 1}}], "Edit Fields2": [{"json": {"description": "event description"}}]}, "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Edit Fields1", "type": "main", "index": 0}, {"node": "Convert to File10", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}, {"node": "Convert to File1", "type": "main", "index": 0}, {"node": "Convert to File2", "type": "main", "index": 0}, {"node": "Convert to File3", "type": "main", "index": 0}, {"node": "Convert to File5", "type": "main", "index": 0}, {"node": "Convert to File6", "type": "main", "index": 0}, {"node": "Convert to File8", "type": "main", "index": 0}, {"node": "Convert to File9", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Extract From File", "type": "main", "index": 0}]]}, "Convert to File1": {"main": [[{"node": "Extract From File1", "type": "main", "index": 0}]]}, "Convert to File2": {"main": [[{"node": "Extract From File2", "type": "main", "index": 0}]]}, "Convert to File3": {"main": [[{"node": "Extract From File3", "type": "main", "index": 0}]]}, "Convert to File5": {"main": [[{"node": "Extract From File4", "type": "main", "index": 0}]]}, "Convert to File6": {"main": [[{"node": "Extract From File5", "type": "main", "index": 0}]]}, "Convert to File7": {"main": [[{"node": "Extract From File6", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Convert to File7", "type": "main", "index": 0}]]}, "Convert to File8": {"main": [[{"node": "Extract From File7", "type": "main", "index": 0}]]}, "Convert to File9": {"main": [[{"node": "Extract From File8", "type": "main", "index": 0}]]}, "Convert to File10": {"main": [[{"node": "Extract From File9", "type": "main", "index": 0}]]}, "Extract From File9": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3287cce3-f02f-45df-9d3b-2f116852c1fb", "meta": {"instanceId": "b888bd11cd1ddbb95450babf3e199556799d999b896f650de768b8370ee50363"}, "id": "ZzoOtOee7hxaNcmp", "tags": []}