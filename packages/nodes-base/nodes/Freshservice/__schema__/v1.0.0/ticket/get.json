{"type": "object", "properties": {"attachments": {"type": "array", "items": {"type": "object", "properties": {"attachment_url": {"type": "string"}, "content_type": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "size": {"type": "integer"}, "updated_at": {"type": "string"}}}}, "cc_emails": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string"}, "created_within_business_hours": {"type": "boolean"}, "custom_fields": {"type": "object", "properties": {"business_impact": {"type": "null"}, "impacted_locations": {"type": "null"}, "major_incident_type": {"type": "null"}, "no_of_customers_impacted": {"type": "null"}, "ticket_has_been_triaged": {"type": "null"}}}, "deleted": {"type": "boolean"}, "description": {"type": "string"}, "description_text": {"type": "string"}, "due_by": {"type": "string"}, "fr_due_by": {"type": "string"}, "fr_escalated": {"type": "boolean"}, "id": {"type": "integer"}, "impact": {"type": "integer"}, "is_escalated": {"type": "boolean"}, "priority": {"type": "integer"}, "reply_cc_emails": {"type": "array", "items": {"type": "string"}}, "requested_for_id": {"type": "integer"}, "requester_id": {"type": "integer"}, "resolution_notes": {"type": "null"}, "resolution_notes_html": {"type": "null"}, "sla_policy_id": {"type": "integer"}, "source": {"type": "integer"}, "spam": {"type": "boolean"}, "status": {"type": "integer"}, "subject": {"type": "string"}, "tasks_dependency_type": {"type": "integer"}, "type": {"type": "string"}, "updated_at": {"type": "string"}, "urgency": {"type": "integer"}, "workspace_id": {"type": "integer"}}, "version": 1}