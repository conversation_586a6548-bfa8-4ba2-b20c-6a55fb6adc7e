{"node": "n8n-nodes-base.function", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "The Function node allows you to execute JavaScript in your workflow. Unlike the Function Item node, this node does not operate on incoming node data per-item. Instead, you must iterate over multiple items of incoming data yourself. This can be useful if you're performing data transformation where you want to manipulate the number of items being outputted by the node (i.e. 1 item is inputted in with nested object, 10 items are outputted without any nested objects)", "categories": ["Development", "Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/"}], "generic": [{"label": "2021 Goals: Level Up Your Vocabulary With Vonage and n8n", "icon": "🎯", "url": "https://n8n.io/blog/2021-goals-level-up-your-vocabulary-with-vonage-and-n8n/"}, {"label": "Learn to Automate Your Factory's Incident Reporting: A Step by Step Guide", "icon": "🏭", "url": "https://n8n.io/blog/learn-to-automate-your-factorys-incident-reporting-a-step-by-step-guide/"}, {"label": "2021: The Year to Automate the New You with n8n", "icon": "☀️", "url": "https://n8n.io/blog/2021-the-year-to-automate-the-new-you-with-n8n/"}, {"label": "Why business process automation with n8n can change your daily life", "icon": "🧬", "url": "https://n8n.io/blog/why-business-process-automation-with-n8n-can-change-your-daily-life/"}, {"label": "Why I chose n8n over <PERSON><PERSON><PERSON> in 2020", "icon": "😍", "url": "https://n8n.io/blog/why-i-chose-n8n-over-zapier-in-2020/"}, {"label": "How to host virtual coffee breaks with n8n", "icon": "☕️", "url": "https://n8n.io/blog/how-to-host-virtual-coffee-breaks-with-n8n/"}, {"label": "Automatically pulling and visualizing data with n8n", "icon": "📈", "url": "https://n8n.io/blog/automatically-pulling-and-visualizing-data-with-n8n/"}, {"label": "Database Monitoring and Alerting with n8n", "icon": "📡", "url": "https://n8n.io/blog/database-monitoring-and-alerting-with-n8n/"}, {"label": "Supercharging your conference registration process with n8n", "icon": "🎫", "url": "https://n8n.io/blog/supercharging-your-conference-registration-process-with-n8n/"}, {"label": "Creating triggers for n8n workflows using polling", "icon": "⏲", "url": "https://n8n.io/blog/creating-triggers-for-n8n-workflows-using-polling/"}, {"label": "6 e-commerce workflows to power up your Shopify s", "icon": "store", "url": "https://n8n.io/blog/no-code-ecommerce-workflow-automations/"}, {"label": "How to build a low-code, self-hosted URL shortener in 3 steps", "icon": "🔗", "url": "https://n8n.io/blog/how-to-build-a-low-code-self-hosted-url-shortener/"}, {"label": "Build your own virtual assistant with n8n: A step by step guide", "icon": "👦", "url": "https://n8n.io/blog/build-your-own-virtual-assistant-with-n8n-a-step-by-step-guide/"}, {"label": "How uProc scraped a multi-page website with a low-code workflow", "icon": " 🕸️", "url": "https://n8n.io/blog/how-uproc-scraped-a-multi-page-website-with-a-low-code-workflow/"}, {"label": "How to automatically give kudos to contributors with GitHub, Slack, and n8n", "icon": "👏", "url": "https://n8n.io/blog/how-to-automatically-give-kudos-to-contributors-with-github-slack-and-n8n/"}, {"label": "5 workflow automations for Matter<PERSON> that we love at n8n", "icon": "🤖", "url": "https://n8n.io/blog/5-workflow-automations-for-mattermost-that-we-love-at-n8n/"}, {"label": "Tracking Time Spent in Meetings With Google Calendar, Twilio, and n8n", "icon": "🗓", "url": "https://n8n.io/blog/tracking-time-spent-in-meetings-with-google-calendar-twilio-and-n8n/"}, {"label": "Creating Error Workflows in n8n", "icon": "🌪", "url": "https://n8n.io/blog/creating-error-workflows-in-n8n/"}, {"label": "Sending Automated Congratulations with Google Sheets, Twilio, and n8n ", "icon": "🙌", "url": "https://n8n.io/blog/sending-automated-congratulations-with-google-sheets-twilio-and-n8n/"}, {"label": "How a Membership Development Manager automates his work and investments", "icon": "📈", "url": "https://n8n.io/blog/how-a-membership-development-manager-automates-his-work-and-investments/"}, {"label": "How Goomer automated their operations with over 200 n8n workflows", "icon": "🛵", "url": "https://n8n.io/blog/how-goomer-automated-their-operations-with-over-200-n8n-workflows/"}, {"label": "7 no-code workflow automations for Amazon Web Services", "url": "https://n8n.io/blog/aws-workflow-automation/"}]}, "alias": ["Code", "Javascript", "Custom Code", "<PERSON><PERSON><PERSON>", "cpde"], "subcategories": {"Core Nodes": ["Data Transformation"]}}