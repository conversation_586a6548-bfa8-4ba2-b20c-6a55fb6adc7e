{"node": "n8n-nodes-base.ftp", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Core <PERSON>", "Data & Storage", "Development", "Utility"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/ftp/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.ftp/"}]}, "alias": ["SFTP", "FTP", "Binary", "File", "Transfer"], "subcategories": {"Core Nodes": ["Files", "Helpers"]}}