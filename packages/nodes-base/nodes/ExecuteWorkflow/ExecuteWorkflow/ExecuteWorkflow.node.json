{"node": "n8n-nodes-base.executeWorkflow", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "The Execute Workflow node can be used when you want your workflow to treat another workflow as a step in your flow. It allows you to modularize your workflows and have a single source of truth for series of actions you perform often. ", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow/"}]}, "alias": ["n8n", "call", "sub", "workflow", "sub-workflow", "subworkflow"], "subcategories": {"Core Nodes": ["Helpers", "Flow"]}}