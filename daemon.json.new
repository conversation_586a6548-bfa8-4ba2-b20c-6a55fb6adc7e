{"registry-mirrors": ["https://docker.m.daocloud.io", "https://docker.nju.edu.cn", "https://mirror.baidubce.com", "https://dockerproxy.com", "https://mirror.iscas.ac.cn", "https://docker.mirrors.ustc.edu.cn", "https://reg-mirror.qiniu.com", "https://hub-mirror.c.163.com", "https://docker.zhai.cm", "https://atomhub.openatom.cn"], "insecure-registries": [], "max-concurrent-downloads": 10, "max-concurrent-uploads": 5, "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3"}, "storage-driver": "overlay2", "runtimes": {"nvidia": {"path": "nvidia-container-runtime", "runtimeArgs": []}}, "default-runtime": "nvidia"}