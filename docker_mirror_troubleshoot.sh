#!/bin/bash

echo "=== Docker镜像源故障排除脚本 ==="
echo

show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -r, --restore    恢复到备份配置"
    echo "  -t, --test       测试当前配置"
    echo "  -s, --status     显示Docker状态"
    echo "  -c, --clean      清理无效镜像源"
    echo "  -h, --help       显示此帮助信息"
    echo
    echo "故障排除步骤:"
    echo "1. 检查Docker服务状态: systemctl status docker"
    echo "2. 检查配置文件: cat /etc/docker/daemon.json"
    echo "3. 测试镜像源连通性: ./test_docker_mirrors.sh"
    echo "4. 如果问题持续，恢复备份: $0 --restore"
}

restore_backup() {
    echo "恢复Docker配置备份..."
    backup_file=$(ls -t /etc/docker/daemon.json.backup.* 2>/dev/null | head -1)
    if [ -n "$backup_file" ]; then
        echo "找到备份文件: $backup_file"
        sudo cp "$backup_file" /etc/docker/daemon.json
        sudo systemctl restart docker
        echo "✓ 配置已恢复并重启Docker服务"
    else
        echo "✗ 未找到备份文件"
        echo "创建最小配置..."
        echo '{"registry-mirrors": ["https://docker.m.daocloud.io"]}' | sudo tee /etc/docker/daemon.json
        sudo systemctl restart docker
    fi
}

test_config() {
    echo "测试当前Docker配置..."
    echo "1. Docker服务状态:"
    systemctl is-active docker
    
    echo "2. 配置文件内容:"
    cat /etc/docker/daemon.json
    
    echo "3. 镜像源配置:"
    docker info | grep -A 10 "Registry Mirrors"
    
    echo "4. 测试连通性:"
    ./test_docker_mirrors.sh
}

show_status() {
    echo "Docker服务状态:"
    systemctl status docker --no-pager -l
    
    echo
    echo "Docker信息:"
    docker info | head -20
}

clean_mirrors() {
    echo "清理无效镜像源配置..."
    # 这里可以添加清理逻辑
    echo "请手动编辑 /etc/docker/daemon.json 移除无效的镜像源"
}

case "$1" in
    -r|--restore)
        restore_backup
        ;;
    -t|--test)
        test_config
        ;;
    -s|--status)
        show_status
        ;;
    -c|--clean)
        clean_mirrors
        ;;
    -h|--help|*)
        show_help
        ;;
esac
